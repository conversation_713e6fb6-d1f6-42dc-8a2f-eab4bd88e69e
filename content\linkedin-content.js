
if (window.linkedInAutomationInjected) {
} else {
    window.linkedInAutomationInjected = true;

class LinkedInAutomation {
    constructor() {
        this.isRunning = false;
        this.currentCampaign = null;
        this.actionDelay = 30000;
        this.dailyLimit = 50;
        this.todayCount = 0;
        this.isRealTimeMode = false;
        this.isAutoCollecting = false;
        this.isAutoCollectionEnabled = true;
        this.currentPageCollected = false;
        this.autoProfileObserver = null;
        this.autoCollectionTimeout = null;
        this.processedProfiles = new Set();

        this.init();
    }

    init() {
        console.log('🚀 CONTENT: LinkedInAutomation init() called');

        chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
            this.handleMessage(message, sendResponse);
        });
        this.loadSettings();
        this.setupAutoDetection();

        // Auto-trigger popup after a delay (for launch tool usage)
        setTimeout(() => {
            this.checkAndAutoOpenPopup();
        }, 3000);
    }

    checkAndAutoOpenPopup() {
        const url = window.location.href;
        console.log('🚀 CONTENT: Checking auto-open for URL:', url);

        // Check if this page was opened via our launch tool (URL parameter)
        if (url.includes('linkedin.com') && url.includes('autoPopup=true')) {
            console.log('🚀 CONTENT: Launched via tool (URL), auto-opening popup');
            this.autoOpenPopup();

            // Clean up the URL parameter
            const cleanUrl = url.replace('?autoPopup=true', '').replace('&autoPopup=true', '');
            window.history.replaceState({}, document.title, cleanUrl);
            return;
        }

        // Simple backup: if on LinkedIn and recently loaded, auto-open
        if (url.includes('linkedin.com')) {
            console.log('🚀 CONTENT: On LinkedIn, checking if should auto-open');
            // Check if page was recently loaded (within 5 seconds)
            const pageLoadTime = performance.now();
            if (pageLoadTime < 5000) {
                console.log('🚀 CONTENT: Recent page load, auto-opening popup');
                this.autoOpenPopup();
            }
        }
    }

    autoOpenPopup() {
        // This method will be called to auto-open the popup
        console.log('🚀 CONTENT: Auto-opening popup triggered');

        // Create floating version of the ORIGINAL popup
        this.createFloatingOriginalPopup();
    }

    createFloatingOriginalPopup() {
        console.log('🚀 CONTENT: Creating floating version of original popup');

        // Remove any existing popup
        const existing = document.getElementById('linkedin-automation-popup');
        if (existing) {
            console.log('🚀 CONTENT: Removing existing popup');
            existing.remove();
        }

        // Create iframe to load the original popup
        const popupContainer = document.createElement('div');
        popupContainer.id = 'linkedin-automation-popup';

        // Create iframe that loads the popup HTML
        const iframe = document.createElement('iframe');
        iframe.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        `;

        // Load the popup HTML content into iframe
        iframe.onload = () => {
            console.log('🚀 CONTENT: Popup iframe loaded');
            this.injectPopupContent(iframe);
        };

        // Set iframe source to popup HTML
        iframe.src = chrome.runtime.getURL('popup/popup.html');

        popupContainer.appendChild(iframe);

        // Style the container to look like extension popup
        popupContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            height: 500px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        `;

        document.body.appendChild(popupContainer);
        console.log('🚀 CONTENT: Original popup container added to page');

        // Make it draggable
        this.makeDraggable(popupContainer);

        // Add close button
        this.addCloseButton(popupContainer);
    }

    injectPopupContent(iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

            // Inject popup CSS
            const cssLink = iframeDoc.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = chrome.runtime.getURL('popup/popup.css');
            iframeDoc.head.appendChild(cssLink);

            // Inject popup JS
            const script = iframeDoc.createElement('script');
            script.src = chrome.runtime.getURL('popup/popup.js');
            iframeDoc.head.appendChild(script);

            console.log('🚀 CONTENT: Popup scripts injected');
        } catch (error) {
            console.error('🚀 CONTENT: Error injecting popup content:', error);
            // Fallback to custom popup
            this.createCustomPopup();
        }
    }

    createCustomPopup() {
        console.log('🚀 CONTENT: Creating custom popup as fallback');

        // Remove iframe container
        const existing = document.getElementById('linkedin-automation-popup');
        if (existing) existing.remove();

        // Create custom popup with original functionality
        const popup = document.createElement('div');
        popup.id = 'linkedin-automation-popup';
        popup.innerHTML = this.getOriginalPopupHTML();

        // Style it exactly like the original
        popup.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            height: 500px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        `;

        document.body.appendChild(popup);

        // Initialize with original popup functionality
        this.initializeOriginalPopupEvents(popup);
        this.makeDraggable(popup);
        this.addCloseButton(popup);

        console.log('🚀 CONTENT: Custom popup with original functionality created');
    }

    addCloseButton(container) {
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.1);
            border: none;
            color: #666;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
        `;

        closeBtn.addEventListener('click', () => {
            container.remove();
        });

        container.appendChild(closeBtn);
    }

    getOriginalPopupHTML() {
        // Return your complete original popup HTML with all modals and functionality
        return `
            <style>
                /* Import your original popup styles */
                .container { display: flex; flex-direction: column; height: 100%; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                .tabs { background: white; border-bottom: 1px solid #e0e0e0; padding: 0; }
                .tab-btn { background: white; border: none; padding: 15px 20px; color: #0077b5; font-weight: 600; border-bottom: 2px solid #0077b5; cursor: pointer; width: 100%; text-align: left; }
                .tab-content { flex: 1; overflow-y: auto; }
                .section { padding: 20px; }
                .section h3 { color: #0077b5; margin-bottom: 20px; font-size: 18px; font-weight: 600; }
                .empty-state { background: #f8f9fa; padding: 40px 20px; border-radius: 8px; text-align: center; color: #999; margin-bottom: 20px; font-style: italic; }
                .btn { padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer; border: none; transition: all 0.3s ease; }
                .btn-primary { background: #0077b5; color: white; }
                .btn-primary:hover { background: #005885; transform: translateY(-1px); }
                .btn-secondary { background: #f8f9fa; color: #333; border: 1px solid #ddd; }
                .btn-secondary:hover { background: #e9ecef; }

                /* Modal styles */
                .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10001; display: none; }
                .modal.show { display: flex; align-items: center; justify-content: center; }
                .modal-content { background: white; border-radius: 12px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto; }
                .campaign-wizard { padding: 0; }
                .wizard-step { display: none; padding: 30px; }
                .wizard-step.active { display: block; }
                .wizard-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
                .wizard-header h3 { margin: 0; color: #0077b5; font-size: 20px; }
                .back-btn { background: none; border: none; font-size: 18px; cursor: pointer; color: #666; }
                .back-btn.hidden { visibility: hidden; }
                .close { font-size: 24px; cursor: pointer; color: #999; }
                .step-indicator { background: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 20px; font-size: 14px; color: #666; }
                .form-group { margin-bottom: 20px; }
                .campaign-input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }
                .wizard-actions { display: flex; justify-content: flex-end; margin-top: 30px; }
                .wizard-next { min-width: 120px; }
                .add-people-options { display: flex; flex-direction: column; gap: 15px; }
                .option-btn { background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; padding: 20px; cursor: pointer; text-align: left; transition: all 0.3s ease; }
                .option-btn:hover { background: #e9ecef; border-color: #0077b5; }
                .option-title { font-weight: 600; color: #333; }
                .or-divider { text-align: center; margin: 20px 0; color: #999; position: relative; }
                .or-divider::before { content: ''; position: absolute; top: 50%; left: 0; right: 0; height: 1px; background: #ddd; z-index: -1; }
                .or-divider { background: white; padding: 0 15px; }
                .csv-upload-area { border: 2px dashed #ddd; border-radius: 8px; padding: 30px; text-align: center; }
                .upload-text { margin-bottom: 15px; color: #666; }
                .upload-btn { background: #0077b5; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; }
                .upload-hint { margin-top: 10px; font-size: 12px; color: #999; }
                .hidden { display: none !important; }
                .search-instructions { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .search-instructions p { margin: 10px 0; color: #666; }
                .search-actions { display: flex; gap: 15px; margin-bottom: 20px; }
                .network-search-options { display: flex; flex-direction: column; gap: 20px; }
                .network-list-option { background: #f8f9fa; padding: 20px; border-radius: 8px; }
                .network-list-option h4 { margin: 0 0 10px 0; color: #0077b5; }
                .network-list-option p { margin: 0 0 15px 0; color: #666; }
                .collection-status { display: flex; flex-direction: column; gap: 15px; }
                .auto-detection-indicator { display: flex; align-items: center; gap: 10px; padding: 15px; background: #e8f4fd; border-radius: 8px; }
                .indicator-dot { width: 8px; height: 8px; background: #0077b5; border-radius: 50%; animation: pulse 2s infinite; }
                @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
                .collected-count { display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f8f9fa; border-radius: 8px; }
                .collected-profiles { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; }
                .profile-selection-section { display: flex; flex-direction: column; gap: 20px; }
                .selection-header { display: flex; justify-content: space-between; align-items: center; }
                .selection-controls { display: flex; gap: 10px; align-items: center; }
                .btn-small { padding: 6px 12px; font-size: 12px; }
                .profiles-selection-list { max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
                .generation-controls { text-align: center; }
                .message-results { background: #f8f9fa; padding: 20px; border-radius: 8px; }
                .messages-container { max-height: 300px; overflow-y: auto; }
                .message-actions { display: flex; gap: 15px; margin-top: 15px; }
                .campaign-creation { text-align: center; margin-top: 30px; }
                .duplicates-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 10002; display: none; align-items: center; justify-content: center; }
                .duplicates-overlay.show { display: flex; }
                .duplicates-content { background: white; border-radius: 12px; padding: 30px; max-width: 500px; width: 90%; }
                .duplicate-profiles { max-height: 200px; overflow-y: auto; margin: 20px 0; }
                .duplicate-actions { display: flex; gap: 15px; justify-content: flex-end; }
                .stats { display: flex; justify-content: space-between; padding: 15px 20px; background: #f8f9fa; border-top: 1px solid #e0e0e0; font-size: 14px; color: #666; }
            </style>

            <div class="container">
                <!-- Single Tab - Campaigns Only -->
                <nav class="tabs">
                    <button class="tab-btn active" data-tab="campaigns">Campaigns</button>
                </nav>

                <!-- Campaigns Tab -->
                <div class="tab-content active" id="campaigns">
                    <div class="section">
                        <h3>Active Campaigns</h3>
                        <div id="campaign-list">
                            <div class="empty-state">No campaigns yet. Create your first campaign!</div>
                        </div>
                        <button class="btn btn-primary" id="create-campaign">+ New Campaign</button>
                    </div>
                </div>

                <!-- Campaign Creation Wizard -->
                <div class="modal" id="campaign-modal">
                    <div class="modal-content campaign-wizard">
                        <!-- Step 1: Campaign Name -->
                        <div class="wizard-step active" id="step-1">
                            <div class="wizard-header">
                                <button class="back-btn hidden">&larr;</button>
                                <h3>Campaign name</h3>
                                <span class="close" title="Close">&times;</span>
                            </div>
                            <div class="step-indicator">Create campaign: step 1 out of 4</div>

                            <div class="form-group">
                                <input type="text" id="campaign-name" placeholder="Enter campaign name" class="campaign-input">
                            </div>

                            <div class="wizard-actions">
                                <button class="btn btn-primary wizard-next" id="next-step-1">NEXT</button>
                            </div>
                        </div>

                        <!-- Step 2: Add People Options -->
                        <div class="wizard-step" id="step-2">
                            <div class="wizard-header">
                                <button class="back-btn" id="back-to-step-1">&larr;</button>
                                <h3>Add people</h3>
                                <span class="close" title="Close">&times;</span>
                            </div>
                            <div class="step-indicator">Create campaign: step 2 out of 4</div>

                            <div class="add-people-options">
                                <button class="option-btn" id="linkedin-search-option">
                                    <div class="option-title">ADD PEOPLE FROM LINKEDIN SEARCH</div>
                                </button>

                                <button class="option-btn" id="sales-navigator-option">
                                    <div class="option-title">ADD PEOPLE FROM SALES NAVIGATOR</div>
                                </button>

                                <button class="option-btn" id="network-option">
                                    <div class="option-title">ADD PEOPLE FROM MY NETWORK</div>
                                </button>

                                <div class="or-divider">Or</div>

                                <div class="csv-upload-area">
                                    <div class="upload-text">Have a CSV file with your prospects' LinkedIn profile URLs?</div>
                                    <button class="upload-btn" id="csv-upload-btn">Click to import the file</button>
                                    <input type="file" id="csv-file-input" accept=".csv" class="hidden">
                                    <div class="upload-hint">or drop it here</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Network Search -->
                        <div class="wizard-step" id="step-3-network">
                            <div class="wizard-header">
                                <button class="back-btn" id="back-to-step-2-from-network">&larr;</button>
                                <h3>Add people from your network</h3>
                                <span class="close" title="Close">&times;</span>
                            </div>
                            <div class="step-indicator">Create campaign: step 3 out of 4</div>

                            <div class="network-search-options">
                                <div class="search-instructions">
                                    <p>- Use native LinkedIn search filters to find people from your network. Scroll the filters to see more fields.</p>
                                    <p>- Once specified, click "Apply LinkedIn filters" to see the results and then press "Start Collecting People" to collect the people.</p>
                                </div>

                                <div class="search-actions">
                                    <button class="btn btn-secondary" id="show-network-filters">SHOW LINKEDIN NETWORK FILTERS</button>
                                    <button class="btn btn-primary" id="start-network-collecting">START COLLECTING PEOPLE</button>
                                </div>

                                <div class="or-divider">Or</div>

                                <div class="network-list-option">
                                    <h4>Browse Your Connections</h4>
                                    <p>View and select from your existing LinkedIn connections</p>
                                    <button class="btn btn-secondary" id="browse-connections">BROWSE MY CONNECTIONS</button>
                                </div>
                            </div>
                        </div>

                        <!-- Additional wizard steps would continue here... -->
                        <!-- For brevity, I'm showing the key structure -->
                    </div>
                </div>

                <!-- Profile Collection Modal -->
                <div class="modal" id="profiles-modal">
                    <div class="modal-content">
                        <span class="close" id="close-profiles" title="Close">&times;</span>
                        <h3>Collected Profiles</h3>
                        <div id="profiles-list"></div>
                        <div class="modal-actions">
                            <button class="btn btn-secondary" id="export-profiles">Export CSV</button>
                            <button class="btn btn-primary" id="create-campaign-from-profiles">Create Campaign</button>
                        </div>
                    </div>
                </div>

                <footer>
                    <div class="stats">
                        <span>Today: <span id="today-count">0</span> connections</span>
                        <span>Total: <span id="total-count">0</span> connections</span>
                    </div>
                </footer>
            </div>

            <!-- Profile URLs Popup Modal -->
            <div id="profile-urls-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Collected Profile URLs</h3>
                        <span class="close" id="close-profile-urls" title="Close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="profile-urls-container">
                            <div class="collection-status">
                                <p>Found <span id="profile-count-display">0</span> profiles</p>
                                <button class="btn btn-primary" id="add-profiles-to-campaign">Add Selected to Campaign</button>
                                <button class="btn btn-secondary" id="select-all-profiles">Select All</button>
                            </div>
                            <div class="profile-urls-list" id="profile-urls-list">
                                <!-- Profile URLs will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeOriginalPopupEvents(popup) {
        console.log('🚀 CONTENT: Initializing original popup events');

        // Main create campaign button
        const createCampaignBtn = popup.querySelector('#create-campaign');
        if (createCampaignBtn) {
            createCampaignBtn.addEventListener('click', () => {
                this.showCampaignModal(popup);
            });
        }

        // Campaign modal events
        this.initializeCampaignWizardEvents(popup);

        // Modal close events
        this.initializeModalCloseEvents(popup);

        console.log('🚀 CONTENT: Original popup events initialized');
    }

    showCampaignModal(popup) {
        const modal = popup.querySelector('#campaign-modal');
        if (modal) {
            modal.classList.add('show');
            modal.style.display = 'flex';
        }
    }

    initializeCampaignWizardEvents(popup) {
        // Step 1: Campaign Name
        const nextStep1 = popup.querySelector('#next-step-1');
        if (nextStep1) {
            nextStep1.addEventListener('click', () => {
                const campaignName = popup.querySelector('#campaign-name').value;
                if (campaignName.trim()) {
                    this.showWizardStep(popup, 'step-2');
                } else {
                    alert('Please enter a campaign name');
                }
            });
        }

        // Back buttons
        const backToStep1 = popup.querySelector('#back-to-step-1');
        if (backToStep1) {
            backToStep1.addEventListener('click', () => {
                this.showWizardStep(popup, 'step-1');
            });
        }

        // Add people options
        const linkedinSearchOption = popup.querySelector('#linkedin-search-option');
        if (linkedinSearchOption) {
            linkedinSearchOption.addEventListener('click', () => {
                // Open LinkedIn search in current tab
                window.location.href = 'https://www.linkedin.com/search/results/people/';
            });
        }

        const networkOption = popup.querySelector('#network-option');
        if (networkOption) {
            networkOption.addEventListener('click', () => {
                this.showWizardStep(popup, 'step-3-network');
            });
        }

        // Network search events
        const showNetworkFilters = popup.querySelector('#show-network-filters');
        if (showNetworkFilters) {
            showNetworkFilters.addEventListener('click', () => {
                window.location.href = 'https://www.linkedin.com/search/results/people/?network=%5B%22F%22%5D&origin=FACETED_SEARCH';
            });
        }

        const startNetworkCollecting = popup.querySelector('#start-network-collecting');
        if (startNetworkCollecting) {
            startNetworkCollecting.addEventListener('click', () => {
                alert('Profile collection started! Navigate to LinkedIn search results to collect profiles.');
            });
        }

        // CSV upload
        const csvUploadBtn = popup.querySelector('#csv-upload-btn');
        const csvFileInput = popup.querySelector('#csv-file-input');
        if (csvUploadBtn && csvFileInput) {
            csvUploadBtn.addEventListener('click', () => {
                csvFileInput.click();
            });

            csvFileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    alert(`CSV file "${file.name}" selected. Processing...`);
                    // Add CSV processing logic here
                }
            });
        }
    }

    showWizardStep(popup, stepId) {
        // Hide all wizard steps
        const allSteps = popup.querySelectorAll('.wizard-step');
        allSteps.forEach(step => {
            step.classList.remove('active');
        });

        // Show target step
        const targetStep = popup.querySelector(`#${stepId}`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }

    initializeModalCloseEvents(popup) {
        // Close buttons for all modals
        const closeButtons = popup.querySelectorAll('.close');
        closeButtons.forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                }
            });
        });

        // Click outside modal to close
        const modals = popup.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                }
            });
        });
    }

    getPopupHTML() {
        return `
            <div class="popup-header" style="
                background: linear-gradient(135deg, #0077b5, #005885);
                color: white;
                padding: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-radius: 8px 8px 0 0;
            ">
                <h1 style="margin: 0; font-size: 18px; font-weight: 600;">LinkedIn Auto Connect</h1>
                <div style="
                    background: #e8f4fd;
                    color: #0077b5;
                    padding: 4px 12px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                ">Ready</div>
            </div>

            <div class="popup-content" style="
                flex: 1;
                overflow-y: auto;
                padding: 0;
                background: white;
            ">
                <!-- Tabs -->
                <nav style="
                    background: white;
                    border-bottom: 1px solid #e0e0e0;
                    padding: 0;
                ">
                    <button style="
                        background: white;
                        border: none;
                        padding: 15px 20px;
                        color: #0077b5;
                        font-weight: 600;
                        border-bottom: 2px solid #0077b5;
                        cursor: pointer;
                        width: 100%;
                        text-align: left;
                    ">Campaigns</button>
                </nav>

                <!-- Content -->
                <div style="padding: 20px;">
                    <h3 style="
                        color: #0077b5;
                        margin-bottom: 20px;
                        font-size: 18px;
                        font-weight: 600;
                    ">Active Campaigns</h3>

                    <div style="
                        background: #f8f9fa;
                        padding: 40px 20px;
                        border-radius: 8px;
                        text-align: center;
                        color: #999;
                        margin-bottom: 20px;
                        font-style: italic;
                    ">No campaigns yet. Create your first campaign!</div>

                    <button class="create-campaign-btn" style="
                        background: #0077b5;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                        width: 100%;
                        transition: all 0.3s ease;
                        margin-bottom: 30px;
                    ">+ New Campaign</button>
                </div>

                <!-- Footer -->
                <div style="
                    border-top: 1px solid #e0e0e0;
                    padding: 15px 20px;
                    background: #f8f9fa;
                    display: flex;
                    justify-content: space-between;
                    font-size: 14px;
                    color: #666;
                ">
                    <span>Today: 0 connections</span>
                    <span>Total: 0 connections</span>
                </div>
            </div>

            <!-- Close button -->
            <button class="close-popup" style="
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">×</button>
        `;
    }

    initializePopupEvents(popup) {
        // Close button
        const closeBtn = popup.querySelector('.close-popup');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                popup.remove();
            });
        }

        // Create campaign button
        const createBtn = popup.querySelector('.create-campaign-btn');
        if (createBtn) {
            createBtn.addEventListener('click', () => {
                // Show message to use extension popup for full functionality
                alert('For full campaign creation, please click the extension icon in your browser toolbar.');
            });

            // Add hover effect
            createBtn.addEventListener('mouseenter', () => {
                createBtn.style.background = '#005885';
            });
            createBtn.addEventListener('mouseleave', () => {
                createBtn.style.background = '#0077b5';
            });
        }
    }

    makeDraggable(popup) {
        const header = popup.querySelector('.popup-header');
        if (!header) return;

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.style.cursor = 'move';

        header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                popup.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }
    
    loadSettings() {
        this.actionDelay = 30 * 1000;
        this.dailyLimit = 50;
        this.todayCount = 0;
    }

    setupAutoDetection() {
        if (this.isProfilePage() && this.isAutoCollectionEnabled) {
            setTimeout(() => {
                this.startAutoCollection();
            }, 2000);
        }
        this.setupPageChangeMonitoring();
    }

    isProfilePage() {
        const url = window.location.href;
        if (url.includes('/in/') && !url.includes('/search/')) {
            return false;
        }
        return url.includes('linkedin.com/search/results/people') ||
               url.includes('linkedin.com/search/people') ||
               url.includes('linkedin.com/mynetwork') ||
               url.includes('linkedin.com/connections') ||
               (url.includes('linkedin.com') && document.querySelector('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result'));
    }

    setupPageChangeMonitoring() {
        let currentUrl = window.location.href;

        const urlObserver = new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                setTimeout(() => {
                    if (this.isProfilePage() && !this.isAutoCollecting && this.isAutoCollectionEnabled) {
                        this.startAutoCollection();
                    }
                }, 2000);
            }
        });

        urlObserver.observe(document.body, {
            childList: true,
            subtree: true
        });

        window.addEventListener('popstate', () => {
            setTimeout(() => {
                if (this.isProfilePage() && !this.isAutoCollecting && this.isAutoCollectionEnabled) {
                    this.startAutoCollection();
                }
            }, 2000);
        });
    }

    async startAutoCollection() {
        if (this.isAutoCollecting) {
            return;
        }

        this.isAutoCollecting = true;

        try {
            if (chrome.runtime?.id) {
                chrome.runtime.sendMessage({
                    action: 'autoCollectionStarted',
                    url: window.location.href
                });
            }
        } catch (error) {
        }

        this.collectAndSendProfiles();
        this.setupContinuousMonitoring();
    }

    async collectAndSendProfiles() {
        const profiles = await this.collectCurrentPageOnly();

        if (profiles.length > 0) {
            this.sendProfilesRealTime(profiles);
        }
    }

    setupContinuousMonitoring() {
        const observer = new MutationObserver((mutations) => {
            let hasNewProfiles = false;

            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const newProfileCards = node.querySelectorAll ?
                                node.querySelectorAll('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result') : [];

                            if (newProfileCards.length > 0) {
                                hasNewProfiles = true;
                            }
                        }
                    });
                }
            });

            if (hasNewProfiles) {
                clearTimeout(this.autoCollectionTimeout);
                this.autoCollectionTimeout = setTimeout(() => {
                    this.collectNewProfilesAuto();
                }, 1500);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        this.autoProfileObserver = observer;
    }

    async collectNewProfilesAuto() {
        if (!this.isAutoCollecting) return;

        const profileCards = document.querySelectorAll('.reusable-search__result-container, [data-chameleon-result-urn], .search-result, .entity-result');
        const newProfiles = [];

        profileCards.forEach((card) => {
            if (card.dataset.autoProcessed) return;

            const profile = this.extractProfileFromCard(card);
            if (profile?.name && profile?.url) {
                newProfiles.push(profile);
                card.dataset.autoProcessed = 'true';
            }
        });

        if (newProfiles.length > 0) {
            this.sendProfilesRealTime(newProfiles);
        }
    }

    stopAutoCollection() {
        this.isAutoCollecting = false;

        if (this.autoProfileObserver) {
            this.autoProfileObserver.disconnect();
            this.autoProfileObserver = null;
        }

        if (this.autoCollectionTimeout) {
            clearTimeout(this.autoCollectionTimeout);
            this.autoCollectionTimeout = null;
        }
    }
    
    handleMessage(message, sendResponse) {
        console.log('🚀 CONTENT: Received message:', message);

        if (!message || !message.action) {
            sendResponse({ error: 'Invalid message format' });
            return;
        }

        switch (message.action) {
            case 'startAutomation':
                this.startAutomation(message.campaign);
                sendResponse({ success: true });
                break;
            case 'stopAutomation':
                this.stopAutomation();
                sendResponse({ success: true });
                break;
            case 'getPageInfo':
                sendResponse(this.getPageInfo());
                break;
            case 'collectProfiles':
                this.collectProfiles().then(profiles => {
                    sendResponse({ profiles });
                });
                return true;
            case 'sendDirectMessage':
                this.handleDirectMessage(message.message, message.profileName, message.profileUrl);
                sendResponse({ success: true });
                break;
            case 'startRealTimeCollection':
                this.isRealTimeMode = true;
                this.currentPageCollected = false;
                setTimeout(() => {
                    this.collectCurrentPageOnly().then(profiles => {
                        if (profiles.length > 0) {
                            this.sendProfilesRealTime(profiles);
                            this.currentPageCollected = true;
                        } else {
                            const alternativeProfiles = this.extractProfilesAlternative();
                            if (alternativeProfiles.length > 0) {
                                this.sendProfilesRealTime(alternativeProfiles.slice(0, 10));
                            }
                        }
                    }).catch(error => {
                        console.error('🚀 CONTENT: Error in real-time collection:', error);
                    });
                }, 1000);

                sendResponse({ success: true });
                return true;
            case 'stopRealTimeCollection':
                this.isRealTimeMode = false;
                this.currentPageCollected = false;
                sendResponse({ success: true });
                return true;
            case 'stopAutoCollection':
                this.stopAutoCollection();
                sendResponse({ success: true });
                return true;
            case 'startAutoCollection':
                if (!this.isAutoCollecting && this.isAutoCollectionEnabled) {
                    this.startAutoCollection();
                }
                sendResponse({ success: true });
                return true;
            case 'enableAutoCollection':
                this.isAutoCollectionEnabled = true;
                if (this.isProfilePage() && !this.isAutoCollecting) {
                    this.startAutoCollection();
                }
                sendResponse({ success: true });
                return true;
            case 'disableAutoCollection':
                this.isAutoCollectionEnabled = false;
                this.stopAutoCollection();
                sendResponse({ success: true });
                return true;
            case 'searchByCompany':
                this.searchByCompany(message.companyName).then(result => {
                    sendResponse(result);
                });
                return true;
            case 'searchNetwork':
                this.searchNetwork(message.criteria).then(profiles => {
                    sendResponse({ profiles: profiles || [] });
                }).catch(error => {
                    console.error('Error in searchNetwork:', error);
                    sendResponse({ profiles: [], error: error.message });
                });
                return true;
            case 'showAutomationInterface':
                // Simple approach: just set a flag that can trigger popup auto-open
                window.linkedinAutomationShouldOpen = true;
                sendResponse({ success: true });
                return true;
            case 'triggerPopupOpen':
                // This will trigger the popup to auto-open
                this.autoOpenPopup();
                sendResponse({ success: true });
                return true;
            default:
                sendResponse({ error: 'Unknown action: ' + message.action });
        }
    }

    isSearchResultsPage() {
        return window.location.href.includes('/search/people/') ||
               window.location.href.includes('/search/results/people/');
    }
    
    startAutomationFromPage() {
        if (this.todayCount >= this.dailyLimit) {
            return;
        }
        this.isRunning = true;
        this.processConnections();
    }
    
    async processConnections() {
        if (!this.isRunning) return;
        
        const connectButtons = this.findConnectButtons();
        
        if (connectButtons.length === 0) {
            this.stopAutomation();
            return;
        }

        for (let i = 0; i < connectButtons.length && this.isRunning; i++) {
            if (this.todayCount >= this.dailyLimit) {
                break;
            }

            const button = connectButtons[i];
            const personInfo = this.extractPersonInfo(button);

            try {
                await this.sendConnectionRequest(button, personInfo);
                this.todayCount++;
                if (i < connectButtons.length - 1) {
                    await this.delay(this.actionDelay);
                }
            } catch (error) {
                console.error('Error sending connection request:', error);
            }
        }

        this.stopAutomation();
    }
    
    findConnectButtons() {
        const selectors = [
            'button[aria-label*="Connect"]',
            'button[data-control-name="connect"]',
            '.search-result__actions button[aria-label*="Invite"]'
        ];

        const buttons = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if ((el.textContent.includes('Connect') || el.getAttribute('aria-label')?.includes('Connect'))
                    && el.offsetParent !== null) {
                    buttons.push(el);
                }
            });
        });

        return buttons;
    }
    
    extractPersonInfo(connectButton) {
        const resultCard = connectButton.closest('.search-result') ||
                           connectButton.closest('.reusable-search__result-container') ||
                           connectButton.closest('[data-chameleon-result-urn]');
        
        let name = 'Unknown';
        let company = '';
        let title = '';
        
        if (resultCard) {
            const nameElement = resultCard.querySelector('.entity-result__title-text a') ||
                               resultCard.querySelector('.search-result__result-link') ||
                               resultCard.querySelector('[data-anonymize="person-name"]');
            
            if (nameElement) {
                name = nameElement.textContent.trim();
            }

            const subtitleElement = resultCard.querySelector('.entity-result__primary-subtitle') ||
                                   resultCard.querySelector('.search-result__truncate');
            
            if (subtitleElement) {
                title = subtitleElement.textContent.trim();
            }
        }
        
        return { name, company, title };
    }
    
    async sendConnectionRequest(button, personInfo) {
        return new Promise((resolve, reject) => {
            try {
                button.click();

                setTimeout(() => {
                    const sendButton = document.querySelector('button[aria-label*="Send without a note"]') ||
                                     document.querySelector('button[data-control-name="send_invite"]') ||
                                     document.querySelector('.send-invite__actions button[aria-label*="Send"]');
                    
                    if (sendButton) {
                        sendButton.click();
                        resolve();
                    } else {
                        const addNoteButton = document.querySelector('button[aria-label*="Add a note"]');
                        if (addNoteButton) {
                            addNoteButton.click();
                            
                            setTimeout(() => {
                                this.sendCustomMessage(personInfo, resolve, reject);
                            }, 1000);
                        } else {
                            reject(new Error('Could not find send button'));
                        }
                    }
                }, 1000);
            } catch (error) {
                reject(error);
            }
        });
    }
    
    async sendCustomMessage(personInfo, resolve, reject) {
        try {

            const messageTemplate = 'Hi {firstName}, I\'d love to connect with you!';
            const personalizedMessage = this.personalizeMessage(messageTemplate, personInfo);

            const messageTextarea = document.querySelector('#custom-message') ||
                                   document.querySelector('textarea[name="message"]') ||
                                   document.querySelector('.send-invite__custom-message textarea');

            if (messageTextarea) {
                messageTextarea.value = personalizedMessage;
                messageTextarea.dispatchEvent(new Event('input', { bubbles: true }));

                setTimeout(() => {
                    const sendButton = document.querySelector('button[aria-label*="Send invitation"]') ||
                                     document.querySelector('.send-invite__actions button[aria-label*="Send"]');

                    if (sendButton) {
                        sendButton.click();
                        resolve();
                    } else {
                        reject(new Error('Could not find send button for custom message'));
                    }
                }, 500);
            } else {
                reject(new Error('Could not find message textarea'));
            }
        } catch (error) {
            reject(error);
        }
    }
    
    personalizeMessage(template, personInfo) {
        const firstName = personInfo.name.split(' ')[0];
        const lastName = personInfo.name.split(' ').slice(1).join(' ');
        
        return template
            .replace(/{firstName}/g, firstName)
            .replace(/{lastName}/g, lastName)
            .replace(/{fullName}/g, personInfo.name)
            .replace(/{company}/g, personInfo.company)
            .replace(/{title}/g, personInfo.title);
    }
    
    startAutomation(campaign) {
        this.currentCampaign = campaign;
        this.startAutomationFromPage();
    }

    stopAutomation() {
        this.isRunning = false;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async handleDirectMessage(message, profileName, profileUrl) {
        try {
            await this.delay(2000);
            const messageButton = await this.findMessageButton();

            if (messageButton) {
                messageButton.click();
                await this.delay(4000);
                const messageInput = await this.findMessageInput();
                if (!messageInput) {
                    return;
                }
                await this.pasteMessageDirectly(messageInput, message);
                await this.delay(1000);
                if (!messageInput.textContent.trim() && !messageInput.value) {
                    await this.pasteUsingClipboard(messageInput, message);
                }
                await this.delay(500);
                if (!messageInput.textContent.trim() && !messageInput.value) {
                    messageInput.textContent = message;
                    messageInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
                await this.clickSendButton();
            }
        } catch (error) {
        }
    }

    async findMessageButton() {
        await this.delay(2000);
        const selectors = [
            'button[aria-label*="Message"]:not([aria-label*="Send"]):not([aria-label*="Share"])',
            'button[data-control-name="message"]',
            '.pv-s-profile-actions button[aria-label*="Message"]',
            '.pvs-profile-actions__action button[aria-label*="Message"]',
            '.message-anywhere-button',
            'a[data-control-name="message"]'
        ];

        for (const selector of selectors) {
            const button = document.querySelector(selector);
            if (button) {
                const text = button.textContent.toLowerCase().trim();
                const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                if (!text.includes('send') &&
                    !text.includes('share') &&
                    !ariaLabel.includes('send') &&
                    !ariaLabel.includes('share') &&
                    !ariaLabel.includes('post')) {
                    return button;
                }
            }
        }

        const buttons = document.querySelectorAll('button, a');
        for (const button of buttons) {
            const text = button.textContent.toLowerCase().trim();
            const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
            if ((text === 'message' || ariaLabel.includes('message')) &&
                !ariaLabel.includes('send') &&
                !ariaLabel.includes('share') &&
                !ariaLabel.includes('post') &&
                !text.includes('send') &&
                !text.includes('share') &&
                !text.includes('more')) {
                return button;
            }
        }
        return null;
    }

    async pasteMessageDirectly(messageInput, message) {
        if (!messageInput) {
            return;
        }
        messageInput.focus();
        await this.delay(500);

        if (messageInput.contentEditable === 'true') {
            messageInput.innerHTML = '';
            messageInput.innerHTML = `<p>${message}</p>`;

            const range = document.createRange();
            const selection = window.getSelection();
            const textNode = messageInput.querySelector('p') || messageInput;
            range.selectNodeContents(textNode);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            messageInput.value = message;
        }

        const events = [
            new Event('focus', { bubbles: true }),
            new Event('input', { bubbles: true }),
            new Event('change', { bubbles: true }),
            new KeyboardEvent('keydown', { bubbles: true, key: 'Enter' }),
            new KeyboardEvent('keyup', { bubbles: true, key: 'Enter' })
        ];
        for (const event of events) {
            messageInput.dispatchEvent(event);
            await this.delay(100);
        }
        messageInput.focus();
        await this.delay(1000);
    }

    async pasteUsingClipboard(messageInput, message) {
        try {

            messageInput.focus();
            await this.delay(300);


            messageInput.textContent = '';

            // Copy message to clipboard
            await navigator.clipboard.writeText(message);
            await this.delay(200);

            // Simulate Ctrl+V paste
            const pasteEvent = new ClipboardEvent('paste', {
                bubbles: true,
                cancelable: true,
                clipboardData: new DataTransfer()
            });

            pasteEvent.clipboardData.setData('text/plain', message);
            messageInput.dispatchEvent(pasteEvent);

            // Trigger input events
            messageInput.dispatchEvent(new Event('input', { bubbles: true }));
            messageInput.dispatchEvent(new Event('change', { bubbles: true }));

            await this.delay(500);
        } catch (error) {
            messageInput.textContent = message;
            messageInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    async findMessageInput() {
        const selectors = [
            '.msg-form__contenteditable',
            '.msg-form__msg-content-container div[contenteditable="true"]',
            'div[data-placeholder*="message"]',
            '.compose-form__message-field',
            'div[contenteditable="true"][data-placeholder]',
            '.msg-form__msg-content-container--scrollable div[contenteditable="true"]',
            '.msg-form__placeholder + div[contenteditable="true"]',
            'div[contenteditable="true"][role="textbox"]',
            '.msg-form div[contenteditable="true"]'
        ];

        for (let attempt = 0; attempt < 8; attempt++) {
            for (const selector of selectors) {
                const input = document.querySelector(selector);
                if (input && input.isContentEditable && input.offsetParent !== null) {
                    return input;
                }
            }
            const allContentEditables = document.querySelectorAll('div[contenteditable="true"]');
            for (const element of allContentEditables) {
                if (element.offsetParent === null) continue;

                const placeholder = element.getAttribute('data-placeholder') ||
                                  element.getAttribute('aria-label') ||
                                  element.getAttribute('placeholder');
                if (placeholder && placeholder.toLowerCase().includes('message')) {
                    return element;
                }

                const parentContainer = element.closest('.msg-form, .compose-form');
                if (parentContainer) {
                    return element;
                }
            }
            await this.delay(1000);
        }

        return null;
    }

    async typeText(element, text) {
        // For contenteditable divs
        if (element.contentEditable === 'true') {
            // Focus the element first
            element.focus();

            // Clear existing content if this is the first text
            if (!element.textContent.trim()) {
                element.textContent = '';
            }

            // Add the text
            element.textContent += text;

            // Move cursor to end
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(element);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);

            // Trigger comprehensive events
            element.dispatchEvent(new Event('focus', { bubbles: true }));
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        } else {
            // For regular input/textarea
            element.focus();
            element.value += text;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        }
    }

    async clickSendButton() {

        await this.delay(2000);

        const sendSelectors = [
            '.msg-form__send-button',
            'button[type="submit"]',
            '.msg-form button[type="submit"]',
            'button[data-control-name="send"]',
            'button[aria-label*="Send"]:not([aria-label*="options"])',
            '.compose-form__send-button',
            '.msg-form__send-btn'
        ];

        for (let attempt = 0; attempt < 10; attempt++) {
            for (const selector of sendSelectors) {
                const button = document.querySelector(selector);
                if (button && !button.disabled && button.offsetParent !== null) {
                    const text = button.textContent.toLowerCase().trim();
                    const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                    if (!text.includes('options') && !ariaLabel.includes('options')) {
                        button.click();
                        await this.delay(1000);
                        return;
                    }
                }
            }
            const buttons = document.querySelectorAll('button');
            for (const button of buttons) {
                const text = button.textContent.toLowerCase().trim();
                const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
                if (text === 'send' &&
                    !text.includes('options') &&
                    !ariaLabel.includes('options') &&
                    !button.disabled &&
                    button.offsetParent !== null &&
                    button.offsetWidth > 0 &&
                    button.offsetHeight > 0) {
                    button.click();
                    await this.delay(1000);
                    return;
                }
            }

            await this.delay(500);
        }
    }
    
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            isSearchPage: this.isSearchResultsPage(),
            connectButtonsCount: this.findConnectButtons().length
        };
    }

    async collectProfiles() {
        const profiles = [];

        if (window.location.href.includes('/mynetwork/')) {
            return this.collectNetworkProfiles();
        }

        const selectors = [
            '.reusable-search__result-container',
            '[data-chameleon-result-urn]',
            '.search-result',
            '.entity-result'
        ];

        let profileCards = [];
        for (const selector of selectors) {
            profileCards = document.querySelectorAll(selector);
            if (profileCards.length > 0) break;
        }

        profileCards.forEach((card) => {
            const profile = this.extractProfileFromCard(card);
            if (profile?.name && profile?.url) {
                profiles.push(profile);
            }
        });

        if (profiles.length === 0) {
            const alternativeProfiles = this.extractProfilesAlternative();
            profiles.push(...alternativeProfiles);
        }

        return profiles;
    }

    async collectCurrentPageOnly() {
        // Use existing collectProfiles method but limit results and send real-time
        const allProfiles = await this.collectProfiles();
        const limitedProfiles = allProfiles.slice(0, 10);

        // Send profiles in real-time as they're collected
        limitedProfiles.forEach(profile => {
            this.sendProfilesRealTime([profile]);
        });

        return limitedProfiles;
    }

    sendProfilesRealTime(profiles) {
        if (!this.isAutoCollectionEnabled) {
            return;
        }

        if (profiles.length > 0) {
            if (!chrome.runtime?.id) {
                this.storeProfilesForPopup(profiles);
                return;
            }
            try {
                chrome.runtime.sendMessage({
                    action: 'addProfilesRealTime',
                    profiles: profiles
                }).catch(() => {
                    this.storeProfilesForPopup(profiles);
                });
            } catch (error) {
                this.storeProfilesForPopup(profiles);
            }
        }
    }

    storeProfilesForPopup() {
        // Storage removed - profiles are handled in memory only
    }

    // Removed duplicate startContinuousCollection() and collectNewProfiles() - functionality exists in setupContinuousMonitoring() and collectNewProfilesAuto()

    fixProfileData(profile) {
        if (!profile.name ||
            profile.name.includes('Status is') ||
            profile.name.includes('offline') ||
            profile.name.includes('reachable') ||
            profile.name.length < 3) {

            if (profile.location) {
                const nameMatch = profile.location.match(/^([A-Za-z\s]+?)(?:View|•|\n)/);
                if (nameMatch && nameMatch[1].trim().length > 2) {
                    profile.name = nameMatch[1].trim();
                }


                const titleMatch = profile.location.match(/Full Stack Developer|Software Engineer|Developer|Engineer|Manager|Director|CEO|CTO|VP|President/i);
                if (titleMatch && !profile.title) {
                    profile.title = titleMatch[0];
                }


                const locationMatch = profile.location.match(/([A-Za-z\s]+,\s*[A-Za-z\s]+)(?:\n|$)/);
                if (locationMatch) {
                    const cleanLocation = locationMatch[1].trim();
                    if (cleanLocation.includes(',') && !cleanLocation.includes('View')) {
                        profile.location = cleanLocation;
                    }
                }
            }
        }

        if (profile.title && profile.title.includes('degree connection')) {
            if (profile.location) {
                const titleMatch = profile.location.match(/\n\s*([A-Za-z\s]+(?:Developer|Engineer|Manager|Director|CEO|CTO|VP|President|Analyst|Consultant|Specialist)[A-Za-z\s]*)/i);
                if (titleMatch) {
                    profile.title = titleMatch[1].trim();
                } else {
                    profile.title = '';
                }
            } else {
                profile.title = '';
            }
        }

        if (profile.title && profile.title.includes(' at ') && !profile.company) {
            const parts = profile.title.split(' at ');
            if (parts.length === 2) {
                profile.title = parts[0].trim();
                profile.company = parts[1].trim();
            }
        }
    }

    extractProfilesAlternative() {
        // Simplified alternative extraction using existing extractProfileFromCard method
        const profiles = [];
        const profileLinks = document.querySelectorAll('a[href*="/in/"]');
        const containers = new Set();

        profileLinks.forEach(link => {
            if (!link.href.includes('/in/') || link.href.includes('?') || link.closest('.processed')) {
                return;
            }

            const container = link.closest('li, div, article');
            if (container && !container.classList.contains('processed')) {
                container.classList.add('processed');
                containers.add(container);
            }
        });

        Array.from(containers).forEach(container => {
            const profile = this.extractProfileFromCard(container);
            if (profile?.name && profile?.url) {
                profiles.push(profile);
            }
        });

        return profiles;
    }

    async collectNetworkProfiles() {
        const profiles = [];

        const selectors = [
            '.discover-entity-type-card',
            '.mn-person-card',
            '[data-test-id="person-card"]',
            '.artdeco-entity-lockup',
            '.discover-person-card'
        ];

        let profileCards = [];
        for (const selector of selectors) {
            profileCards = document.querySelectorAll(selector);
            if (profileCards.length > 0) break;
        }

        profileCards.forEach(card => {
            const profile = this.extractProfileFromCard(card, true); // Use unified extraction with network flag
            if (profile?.name && profile?.url) {
                profiles.push(profile);
            }
        });

        return profiles;
    }

    extractProfileFromCard(card, isNetworkPage = false) {
        const profile = {
            name: '',
            url: '',
            company: '',
            title: '',
            location: '',
            industry: '',
            profilePic: '',
            collectedAt: new Date().toISOString()
        };

        try {
            const nameSelectors = isNetworkPage ? [
                'a[href*="/in/"]',
                '.discover-entity-type-card__link',
                '.mn-person-card__link',
                '.artdeco-entity-lockup__title a'
            ] : [
                '.entity-result__title-text a',
                '.search-result__result-link',
                'a[href*="/in/"]',
                '.app-aware-link'
            ];

            let nameLink = null;
            for (const selector of nameSelectors) {
                nameLink = card.querySelector(selector);
                if (nameLink) break;
            }

            if (nameLink) {
                let nameText = nameLink.textContent.trim();

                if (nameText.includes('View') && nameText.includes('profile')) {
                    const match = nameText.match(/^(.+?)(?:View|•|\n)/);
                    if (match) {
                        nameText = match[1].trim();
                    }
                }

                profile.name = nameText;
                profile.url = nameLink.href || '';
            } else {
                const nameSelectors = [
                    'span[aria-hidden="true"]',
                    '.t-16.t-black.t-bold',
                    '[data-anonymize="person-name"] span',
                    '.entity-result__title-text span',
                    '.search-result__result-link span',
                    '.artdeco-entity-lockup__title span',
                    'span.t-16',
                    'span.t-bold'
                ];

                let nameSpan = null;
                for (const selector of nameSelectors) {
                    nameSpan = card.querySelector(selector);
                    if (nameSpan && nameSpan.textContent.trim() &&
                        !nameSpan.textContent.includes('Status') &&
                        !nameSpan.textContent.includes('View') &&
                        nameSpan.textContent.length > 2) {
                        break;
                    }
                    nameSpan = null;
                }

                if (nameSpan) {
                    profile.name = nameSpan.textContent.trim();
                    const parentLink = nameSpan.closest('a') || card.querySelector('a[href*="/in/"]');
                    if (parentLink) profile.url = parentLink.href;
                } else {
                    const allLinks = card.querySelectorAll('a[href*="/in/"]');
                    for (const link of allLinks) {
                        const text = link.textContent.trim();
                        if (text && text.length > 2 &&
                            !text.includes('Status') &&
                            !text.includes('View') &&
                            !text.includes('•') &&
                            text.split(' ').length >= 2) {
                            profile.name = text;
                            profile.url = link.href;
                            break;
                        }
                    }
                }
            }

            if (profile.url) {
                if (profile.url.startsWith('/')) {
                    profile.url = 'https://www.linkedin.com' + profile.url;
                }
                if (profile.url.includes('?')) {
                    profile.url = profile.url.split('?')[0];
                }
                profile.url = profile.url.replace(/\/$/, '');
            }

            const imgSelectors = [
                '.entity-result__image img',
                '.presence-entity__image img',
                '.discover-entity-type-card__image img',
                '.mn-person-card__picture img',
                '.artdeco-entity-lockup__image img',
                'img[alt*="profile"]',
                'img[alt*="Photo"]',
                'img[data-ghost-classes]',
                'img[src*="profile"]',
                'img'
            ];

            for (const selector of imgSelectors) {
                const imgElement = card.querySelector(selector);
                if (imgElement?.src &&
                    !imgElement.src.includes('data:image') &&
                    !imgElement.src.includes('ghost') &&
                    imgElement.src.includes('http')) {
                    profile.profilePic = imgElement.src;
                    break;
                }
            }

            const subtitleSelectors = isNetworkPage ? [
                '.discover-entity-type-card__occupation',
                '.mn-person-card__occupation',
                '.artdeco-entity-lockup__subtitle'
            ] : [
                '.entity-result__primary-subtitle',
                '.search-result__truncate',
                '.t-14.t-normal'
            ];

            for (const selector of subtitleSelectors) {
                const subtitleElement = card.querySelector(selector);
                if (subtitleElement) {
                    const subtitle = subtitleElement.textContent.trim();
                    const atIndex = subtitle.toLowerCase().indexOf(' at ');
                    if (atIndex !== -1) {
                        profile.title = subtitle.substring(0, atIndex).trim();
                        profile.company = subtitle.substring(atIndex + 4).trim();
                    } else {
                        profile.title = subtitle;
                    }
                    break;
                }
            }

            const locationSelectors = [
                '.entity-result__secondary-subtitle',
                '[data-anonymize="location"]',
                '.t-12.t-black--light'
            ];

            for (const selector of locationSelectors) {
                const locationElement = card.querySelector(selector);
                if (locationElement) {
                    profile.location = locationElement.textContent.trim();
                    break;
                }
            }

            this.fixProfileData(profile);
            if (!profile.name || !profile.url || !profile.url.includes('/in/')) {

                return null;
            }

            return profile;

        } catch (error) {
            console.error('Error extracting profile data:', error);
            return null;
        }
    }

    async searchByCompany(companyName) {
        try {
            const searchUrl = `https://www.linkedin.com/search/results/people/?keywords=${encodeURIComponent(companyName)}&origin=GLOBAL_SEARCH_HEADER`;
            window.location.href = searchUrl;

            return { success: true, message: `Searching for employees at ${companyName}` };
        } catch (error) {
            console.error('Error searching by company:', error);
            return { success: false, message: error.message };
        }
    }

    async searchNetwork(criteria) {
        try {

            const profiles = [];
            let scrollAttempts = 0;
            const maxScrollAttempts = 5;

            this.setupContinuousMonitoring();

            if (criteria.type === 'search' || window.location.href.includes('search/results/people')) {

                let searchResults = this.getSearchResultElements();

                searchResults.forEach((card) => {
                    if (profiles.length < 20) {
                        const profile = this.extractProfileFromCard(card);
                        if (profile && profile.name && profile.url) {
                            profile.source = 'network-search';
                            profiles.push(profile);

                        }
                    }
                });

                while (scrollAttempts < maxScrollAttempts && profiles.length < 20) {
                    scrollAttempts++;

                    const initialCount = profiles.length;

                    if (scrollAttempts <= 3) {
                        window.scrollBy(0, window.innerHeight);
                        await this.delay(2000);
                        window.scrollTo(0, document.body.scrollHeight);

                    } else {
                        window.scrollBy(0, -window.innerHeight);
                        await this.delay(2000);

                        if (scrollAttempts === maxScrollAttempts) {
                            window.scrollTo(0, 0);

                        }
                    }

                    await this.delay(2000);
                    searchResults = this.getSearchResultElements();
                    searchResults.forEach((card) => {
                        if (profiles.length < 20) {
                            const profile = this.extractProfileFromCard(card);
                            if (profile && profile.name && profile.url) {
                                const isDuplicate = profiles.some(p => p.url === profile.url);
                                if (!isDuplicate) {
                                    profile.source = 'network-search';
                                    profiles.push(profile);

                                }
                            }
                        }
                    });

                    const newProfilesCount = profiles.length - initialCount;

                    if (newProfilesCount === 0 && scrollAttempts >= 2) {

                        break;
                    }
                }

            } else if (criteria.type === 'connections' || window.location.href.includes('mynetwork') || window.location.href.includes('connections')) {
                let connectionCards = document.querySelectorAll('.mn-connection-card');
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('.connection-card');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('[data-control-name="connection_profile"]');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('.artdeco-entity-lockup');
                }
                if (connectionCards.length === 0) {
                    connectionCards = document.querySelectorAll('li');
                }



                connectionCards.forEach((card, index) => {
                    if (index < 20) { // Process more profiles for better real-time experience
                        const profile = this.extractProfileFromCard(card, true); // Use unified extraction
                        if (profile?.name && profile?.url) {
                            profile.source = 'connections';
                            profiles.push(profile);

                            if (profiles.length <= 3 || profiles.length % 2 === 0) {
                                this.sendProfilesRealTime([profile]);
                            }
                        }
                    }
                });
            }

            return profiles;
        } catch (error) {
            console.error('Error searching network:', error);
            return [];
        }
    }

    getSearchResultElements() {
        let elements = document.querySelectorAll('.search-result');
        if (elements.length > 0) return elements;

        elements = document.querySelectorAll('.reusable-search__result-container');
        if (elements.length > 0) return elements;

        elements = document.querySelectorAll('[data-chameleon-result-urn]');
        if (elements.length > 0) return elements;

        elements = document.querySelectorAll('li[data-reusable-search-result]');
        if (elements.length > 0) return elements;

        elements = document.querySelectorAll('.entity-result');
        if (elements.length > 0) return elements;

        elements = document.querySelectorAll('li');
        return Array.from(elements).filter(li => {
            return li.querySelector('a[href*="/in/"]') || li.querySelector('a[href*="linkedin.com/in/"]');
        });
    }

}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.linkedInAutomation = new LinkedInAutomation();
    });
} else {
    window.linkedInAutomation = new LinkedInAutomation();
}

}
